// pages/join/index.js
const api = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20,
    navbarHeight: 88,
    gameId: '',
    inviteCode: '',
    game: null,
    isLoading: true,
    joinSuccess: false,
    errorMessage: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息设置状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 获取分享参数
    const {
      gameId,
      inviteCode
    } = options;

    if (!gameId) {
      this.setData({
        isLoading: false,
        errorMessage: '无效的邀请链接'
      });
      return;
    }

    this.setData({
      gameId: gameId,
      inviteCode: inviteCode || gameId
    });

    // 加载游戏信息
    this.loadGameInfo();
  },

  /**
   * 加载游戏信息
   */
  loadGameInfo() {
    this.setData({
      isLoading: true
    });

    // 先尝试从API加载
    api.getGame(this.data.gameId).then(game => {
      if (game) {
        this.setData({
          game: game,
          isLoading: false
        });
      } else {
        // API失败，尝试从本地存储加载
        this.loadGameFromLocal();
      }
    }).catch(err => {
      // API失败，尝试从本地存储加载
      this.loadGameFromLocal();
    });
  },

  /**
   * 从本地存储加载游戏信息
   */
  loadGameFromLocal() {
    try {
      const games = wx.getStorageSync('games') || [];
      const game = games.find(g => g.id === this.data.gameId);

      if (game) {
        this.setData({
          game: game,
          isLoading: false
        });
      } else {
        this.setData({
          isLoading: false,
          errorMessage: '游戏不存在或已结束'
        });
      }
    } catch (error) {
      this.setData({
        isLoading: false,
        errorMessage: '加载游戏信息失败'
      });
    }
  },

  /**
   * 加入游戏
   */
  joinGame() {
    if (!this.data.game) {
      wx.showToast({
        title: '游戏信息加载失败',
        icon: 'none'
      });
      return;
    }

    // 检查游戏是否已结束
    if (this.data.game.settled) {
      wx.showToast({
        title: '游戏已结束，无法加入',
        icon: 'none'
      });
      return;
    }

    // 直接跳转到游戏页面
    wx.redirectTo({
      url: `/pages/score/index?id=${this.data.gameId}`
    }).catch(err => {
      wx.showToast({
        title: '跳转失败',
        icon: 'none'
      });
    });
  },

  /**
   * 返回首页
   */
  goHome() {
    wx.switchTab({
      url: '/pages/gameList/index'
    }).catch(err => {
      wx.navigateTo({
        url: '/pages/gameList/index'
      });
    });
  },

  /**
   * 导航栏高度回调
   */
  onNavbarHeight(e) {
    this.setData({
      navbarHeight: e.detail.height
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const {
      game
    } = this.data;

    if (!game) {
      return {
        title: '邀请你加入游戏',
        path: '/pages/gameList/index'
      };
    }

    return {
      title: `邀请你加入「${game.name}」`,
      path: `/pages/join/index?gameId=${game.id}&inviteCode=${this.data.inviteCode}`,
      imageUrl: '/static/images/logo-small.png'
    };
  }
})