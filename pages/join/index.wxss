/* pages/join/index.wxss */

.join-page {
  min-height: 100vh;
  background-color: #f8f4e9;
}

.page-content {
  padding: 32rpx;
  min-height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 400rpx;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 32rpx;
  animation: bounce 1.5s infinite;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 400rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 32rpx;
}

.error-text {
  font-size: 32rpx;
  color: #8B0000;
  margin-bottom: 48rpx;
  text-align: center;
}

/* 游戏信息容器 */
.game-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 游戏卡片 */
.game-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border-left: 8rpx solid #d4af37;
}

.game-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.game-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B0000;
}

/* 游戏详情 */
.game-details {
  margin-bottom: 32rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 玩家列表 */
.players-section {
  border-top: 1rpx solid #eee;
  padding-top: 24rpx;
}

.players-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.players-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 12rpx;
  background: #f8f4e9;
  border-radius: 12rpx;
}

.player-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.player-info {
  display: flex;
  flex-direction: column;
}

.player-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.player-score {
  font-size: 24rpx;
  color: #666;
}

/* 邀请信息 */
.invite-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.invite-icon {
  font-size: 64rpx;
  margin-bottom: 24rpx;
}

.invite-text {
  font-size: 32rpx;
  color: #d4af37;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.invite-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  margin-top: auto;
  padding-top: 32rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.3);
}

.btn-secondary {
  background: white;
  color: #666;
  border: 2rpx solid #eee;
}

.btn-secondary:active {
  background: #f5f5f5;
}

.join-btn {
  font-size: 36rpx;
  height: 96rpx;
}

.home-btn {
  background: transparent;
  color: #999;
  border: 1rpx solid #ddd;
}
