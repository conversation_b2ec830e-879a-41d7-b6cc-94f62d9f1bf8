<!--pages/join/index.wxml-->
<view class="join-page">
  <!-- 自定义导航栏 -->
  <custom-navbar
    title="加入游戏"
    show-back="{{true}}"
    show-home="{{true}}"
    bind:height="onNavbarHeight">
  </custom-navbar>

  <!-- 页面内容 -->
  <view class="page-content" style="padding-top: {{navbarHeight}}px;">

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-icon">🎮</view>
      <text class="loading-text">正在加载游戏信息...</text>
    </view>

    <!-- 错误状态 -->
    <view class="error-container" wx:elif="{{errorMessage}}">
      <view class="error-icon">❌</view>
      <text class="error-text">{{errorMessage}}</text>
      <button class="btn btn-primary" bindtap="goHome">返回首页</button>
    </view>

    <!-- 游戏信息 -->
    <view class="game-info-container" wx:else>
      <!-- 游戏卡片 -->
      <view class="game-card">
        <view class="game-header">
          <view class="game-icon">🎯</view>
          <view class="game-title">{{game.name}}</view>
        </view>

        <view class="game-details">
          <view class="detail-item">
            <text class="detail-label">游戏类型：</text>
            <text class="detail-value">{{game.type === 'mahjong' ? '麻将' : game.type === 'poker' ? '扑克' : game.type === 'board' ? '棋类' : '其他'}}</text>
          </view>

          <view class="detail-item">
            <text class="detail-label">当前人数：</text>
            <text class="detail-value">{{game.players.length}}人</text>
          </view>

          <view class="detail-item">
            <text class="detail-label">初始分数：</text>
            <text class="detail-value">{{game.initialScore}}分</text>
          </view>

          <view class="detail-item" wx:if="{{game.rounds && game.rounds.length > 0}}">
            <text class="detail-label">游戏轮次：</text>
            <text class="detail-value">第{{game.rounds.length}}轮</text>
          </view>
        </view>

        <!-- 玩家列表 -->
        <view class="players-section" wx:if="{{game.players && game.players.length > 0}}">
          <view class="players-title">当前玩家</view>
          <view class="players-list">
            <view class="player-item" wx:for="{{game.players}}" wx:key="id">
              <image class="player-avatar" src="{{item.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
              <view class="player-info">
                <text class="player-name">{{item.name}}</text>
                <text class="player-score">{{item.score}}分</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 邀请信息 -->
      <view class="invite-info">
        <view class="invite-icon">🎉</view>
        <text class="invite-text">你被邀请加入这个游戏</text>
        <text class="invite-desc">点击下方按钮即可加入游戏</text>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="btn btn-primary join-btn" bindtap="joinGame" wx:if="{{!game.settled}}">
          加入游戏
        </button>

        <button class="btn btn-secondary" bindtap="goHome" wx:if="{{game.settled}}">
          游戏已结束
        </button>

        <button class="btn btn-secondary home-btn" bindtap="goHome">
          返回首页
        </button>
      </view>
    </view>
  </view>
</view>